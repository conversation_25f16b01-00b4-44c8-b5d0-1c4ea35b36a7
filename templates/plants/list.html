{% extends "base.html" %}
{% block title %}Plants{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Plant Database</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Manage your personal plant collection</p>
        </div>
        <a href="/plants/new" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded transition-colors shadow-md">
            Add New Plant
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white dark:bg-sage-800 p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sage-600 dark:text-sage-400">Total Plants</p>
                    <p class="text-2xl font-semibold text-sage-900 dark:text-sage-100">{{ plants|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-sage-800 p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sage-600 dark:text-sage-400">With Latin Names</p>
                    <p class="text-2xl font-semibold text-sage-900 dark:text-sage-100">{{ plants_with_latin_names }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-sage-800 p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sage-600 dark:text-sage-400">Varieties</p>
                    <p class="text-2xl font-semibold text-sage-900 dark:text-sage-100">{{ plants_with_varieties }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Plants Table -->
    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-sage-200 dark:border-sage-700">
            <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100">Plant Collection</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sage-200 dark:divide-sage-700">
                <thead class="bg-sage-50 dark:bg-sage-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Plant
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Latin Name
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Variety
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-sage-800 divide-y divide-sage-200 dark:divide-sage-700">
                    {% for plant in plants %}
                    <tr class="hover:bg-sage-50 dark:hover:bg-sage-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-sage-900 dark:text-sage-100">
                                        {{ plant.name }}
                                    </div>
                                    {% if plant.note %}
                                    <div class="text-sm text-sage-500 dark:text-sage-400">
                                        {% if plant.note|length > 50 %}{{ plant.note|slice(end=50) }}...{% else %}{{ plant.note }}{% endif %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">
                            {{ plant.latin_name | default(value="Not specified") }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">
                            {{ plant.variety | default(value="Standard") }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="/plants/{{ plant.id }}/edit" class="text-sage-600 hover:text-sage-900 dark:text-sage-400 dark:hover:text-sage-300 mr-3">
                                Edit
                            </a>
                            <form method="post" action="/plants/{{ plant.id }}/delete" class="inline" onsubmit="return confirm('Are you sure you want to delete this plant?')">
                                <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                    Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if plants|length == 0 %}
        <div class="px-6 py-8 text-center">
            <p class="text-sage-500 dark:text-sage-400">No plants found in your collection.</p>
            <a href="/plants/new" class="mt-2 inline-block text-sage-600 hover:text-sage-500 dark:text-sage-400 dark:hover:text-sage-300 font-medium">
                Add your first plant →
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
