{% extends "base.html" %}
{% block title %}Register{% endblock %}
{% block content %}
<div class="max-w-md mx-auto">
  <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 mt-10">
    <h2 class="text-2xl font-bold text-sage-900 dark:text-sage-100 mb-6 text-center">Create an Account</h2>

    {% if error %}
    <div class="mb-6 p-4 bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-200 rounded-md">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        {{ error }}
      </div>
    </div>
    {% endif %}

    <form method="post" action="/auth/register" class="space-y-6">
      <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

      <div>
        <label for="username" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">Username</label>
        <input type="text" id="username" name="username" required
               class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
      </div>

        <label for="password" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">Password</label>
        <input type="password" id="password" name="password" required
               class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
      </div>

      <div>
        <label for="password_confirm" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">Confirm Password</label>
        <input type="password" id="password_confirm" name="password_confirm" required
               class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
      </div>

      <button type="submit" class="w-full bg-sage-600 hover:bg-sage-700 text-white font-medium py-2 px-4 rounded-md transition-colors shadow-sm">
        Register
      </button>
    </form>

    <p class="mt-6 text-center text-sage-600 dark:text-sage-400">
      Already have an account?
      <a href="/auth/login" class="text-sage-600 hover:text-sage-500 dark:text-sage-400 dark:hover:text-sage-300 font-medium underline">
        Login here
      </a>
    </p>
  </div>
</div>
{% endblock %}
