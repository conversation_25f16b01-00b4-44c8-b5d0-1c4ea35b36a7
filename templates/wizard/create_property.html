{# templates/wizard/create_property.html #}
{% extends "base.html" %}
{% block title %}Create Property{% endblock %}
{% block content %}
<div class="max-w-md mx-auto">
    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-sage-900 dark:text-sage-100 mb-6">Property Details</h1>

        <form method="post" action="/wizard/property" class="space-y-6">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

            <div>
                <label for="name" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">Property Name</label>
                <input type="text" id="name" name="name" required
                       class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
            </div>

            <div>
                <label for="outside_area" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">Outside Area (m²)</label>
                <input type="number" id="outside_area" name="outside_area" min="0" placeholder="0"
                       class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
            </div>

            <div>
                <label for="inside_area" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">Inside Area (m²)</label>
                <input type="number" id="inside_area" name="inside_area" min="0" placeholder="0"
                       class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
            </div>

            <div>
                <label for="floors" class="block text-sm font-medium text-sage-700 dark:text-sage-200 mb-2">Number of Floors</label>
                <input type="number" id="floors" name="floors" min="0" placeholder="0"
                       class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
            </div>

            <button type="submit" class="w-full bg-sage-600 hover:bg-sage-700 text-white font-medium py-2 px-4 rounded-md transition-colors shadow-sm">
                Next
            </button>
        </form>
    </div>
</div>
{% endblock %}
