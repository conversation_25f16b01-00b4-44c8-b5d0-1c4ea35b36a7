{% extends "base.html" %}
{% block title %}Seeds{% endblock %}
{% block content %}
<div class="max-w-6xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Seed Inventory</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Track your seed collection and viability</p>
        </div>
        <a href="/seeds/new" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded transition-colors shadow-md">
            Add New Seed
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white dark:bg-sage-800 p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-amber-100 dark:bg-amber-900">
                    <svg class="w-6 h-6 text-amber-600 dark:text-amber-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sage-600 dark:text-sage-400">Total Seeds</p>
                    <p class="text-2xl font-semibold text-sage-900 dark:text-sage-100">{{ seeds|length }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-sage-800 p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sage-600 dark:text-sage-400">Fresh Seeds</p>
                    <p class="text-2xl font-semibold text-sage-900 dark:text-sage-100">{{ fresh_seeds_count }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-sage-800 p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sage-600 dark:text-sage-400">Expiring Soon</p>
                    <p class="text-2xl font-semibold text-sage-900 dark:text-sage-100">{{ expiring_soon_count }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-sage-800 p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-sage-600 dark:text-sage-400">Expired</p>
                    <p class="text-2xl font-semibold text-sage-900 dark:text-sage-100">{{ expired_seeds_count }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Seeds Table -->
    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-sage-200 dark:border-sage-700">
            <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100">Seed Collection</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-sage-200 dark:divide-sage-700">
                <thead class="bg-sage-50 dark:bg-sage-700">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Seed
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Acquisition
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Expiration
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-sage-500 dark:text-sage-300 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-sage-800 divide-y divide-sage-200 dark:divide-sage-700">
                    {% for seed in seeds %}
                    <tr class="hover:bg-sage-50 dark:hover:bg-sage-700 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-amber-100 dark:bg-amber-900 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-amber-600 dark:text-amber-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-sage-900 dark:text-sage-100">
                                        {{ seed.name }}
                                    </div>
                                    {% if seed.note %}
                                    <div class="text-sm text-sage-500 dark:text-sage-400">
                                        {% if seed.note|length > 50 %}{{ seed.note|slice(end=50) }}...{% else %}{{ seed.note }}{% endif %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">
                            {{ seed.acquisition_year }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-sage-900 dark:text-sage-100">
                            {{ seed.expiration_year }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if seed.expiration_year < 2024 %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    Expired
                                </span>
                            {% elif seed.expiration_year == 2025 %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    Expiring Soon
                                </span>
                            {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    Fresh
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="/seeds/{{ seed.id }}/edit" class="text-sage-600 hover:text-sage-900 dark:text-sage-400 dark:hover:text-sage-300 mr-3">
                                Edit
                            </a>
                            <form method="post" action="/seeds/{{ seed.id }}/delete" class="inline" onsubmit="return confirm('Are you sure you want to delete this seed?')">
                                <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                    Delete
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if seeds|length == 0 %}
        <div class="px-6 py-8 text-center">
            <p class="text-sage-500 dark:text-sage-400">No seeds found in your inventory.</p>
            <a href="/seeds/new" class="mt-2 inline-block text-sage-600 hover:text-sage-500 dark:text-sage-400 dark:hover:text-sage-300 font-medium">
                Add your first seed →
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
