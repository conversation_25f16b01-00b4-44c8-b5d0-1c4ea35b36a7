{% extends "base.html" %}
{% block title %}Plant Scraper Results - Admin{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-sage-900 dark:text-sage-100">Plant Information Scraped</h1>
            <p class="text-sage-600 dark:text-sage-400 mt-2">Results for: {{ plant_name }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="/admin/herba-db" class="bg-sage-500 hover:bg-sage-600 text-white px-4 py-2 rounded transition-colors">
                Back to HerbaDB
            </a>
        </div>
    </div>

    <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6 mb-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Basic Information</h2>
                
                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Plant Name</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.name }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Scientific Name</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.scientific_name | default(value="Not specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Common Names</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.common_names | join(sep=", ") | default(value="None specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Plant Type</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.plant_type | default(value="Not specified") }}</p>
                </div>
            </div>

            <!-- Growing Information -->
            <div class="space-y-4">
                <h2 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Growing Information</h2>
                
                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Watering Needs</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.watering_needs | default(value="Not specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Light Requirements</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.light_requirements | default(value="Not specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Soil Type</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.soil_type | default(value="Not specified") }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-1">Hardiness Zone</label>
                    <p class="text-sage-900 dark:text-sage-100 bg-sage-50 dark:bg-sage-700 p-2 rounded">{{ plant_info.hardiness_zone | default(value="Not specified") }}</p>
                </div>
            </div>
        </div>

        <!-- Description -->
        {% if plant_info.description %}
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Description</h3>
            <p class="text-sage-700 dark:text-sage-300 bg-sage-50 dark:bg-sage-700 p-4 rounded">{{ plant_info.description }}</p>
        </div>
        {% endif %}

        <!-- Special Features -->
        {% if plant_info.special_features and plant_info.special_features|length > 0 %}
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Special Features</h3>
            <div class="flex flex-wrap gap-2">
                {% for feature in plant_info.special_features %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-sage-100 dark:bg-sage-700 text-sage-800 dark:text-sage-200">
                    {{ feature }}
                </span>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Care Instructions -->
        {% if plant_info.care_instructions %}
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-sage-900 dark:text-sage-100 mb-2">Care Instructions</h3>
            <p class="text-sage-700 dark:text-sage-300 bg-sage-50 dark:bg-sage-700 p-4 rounded">{{ plant_info.care_instructions }}</p>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="mt-8 flex justify-between items-center">
            <div class="flex space-x-3">
                <button onclick="createHerbaEntry()" class="bg-sage-600 hover:bg-sage-700 text-white px-4 py-2 rounded transition-colors">
                    Create HerbaDB Entry
                </button>
                <button onclick="rescrape()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors">
                    Re-scrape Information
                </button>
            </div>
            <a href="/admin/herba-db" class="bg-sage-300 hover:bg-sage-400 text-sage-700 px-4 py-2 rounded transition-colors">
                Back to HerbaDB
            </a>
        </div>
    </div>
</div>

<script>
function createHerbaEntry() {
    if (confirm('Create a new HerbaDB entry with this information?')) {
        // TODO: Implement HerbaDB entry creation
        fetch('/admin/herba-db/create-from-scrape', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                plant_info: {{ plant_info | tojson }},
                plant_name: '{{ plant_name }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('HerbaDB entry created successfully!');
                window.location.href = '/admin/herba-db';
            } else {
                alert('Error creating entry: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }
}

function rescrape() {
    if (confirm('Re-scrape information for {{ plant_name }}?')) {
        window.location.href = '/admin/herba-db/scrape?plant_name={{ plant_name | urlencode }}';
    }
}
</script>
{% endblock %}
