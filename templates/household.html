{% extends "base.html" %}
{% block title %}Household Management{% endblock %}
{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="bg-white dark:bg-sage-800 rounded-lg shadow-md p-6">
    <h2 class="text-2xl font-bold text-sage-900 dark:text-sage-100 mb-6">Manage Household</h2>

    <form hx-post="/household/create" hx-target="#household-message" hx-swap="outerHTML" class="mb-8">
      <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
      <div class="mb-4">
        <label for="household-name" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-2">Household Name</label>
        <input type="text" id="household-name" name="household_name" required
               class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
      </div>
      <button type="submit" class="bg-sage-600 hover:bg-sage-700 text-white font-medium px-4 py-2 rounded-md transition-colors">
        Create Household
      </button>
    </form>
    <div id="household-message"></div>

    <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Household Members</h3>
    <div id="household-members" class="mb-8">
      <!-- Household members will be dynamically loaded here -->
      <div class="bg-sage-50 dark:bg-sage-700 rounded-md p-4">
        <ul class="space-y-2">
          <li class="flex justify-between items-center p-2 bg-white dark:bg-sage-600 rounded">
            <span class="text-sage-900 dark:text-sage-100">Member 1 - Admin</span>
            <button hx-post="/household/remove_member" data-member-id="1"
                    class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors">
              Remove
            </button>
          </li>
          <li class="flex justify-between items-center p-2 bg-white dark:bg-sage-600 rounded">
            <span class="text-sage-900 dark:text-sage-100">Member 2 - Viewer</span>
            <button hx-post="/household/remove_member" data-member-id="2"
                    class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm transition-colors">
              Remove
            </button>
          </li>
        </ul>
      </div>
    </div>

    <h3 class="text-xl font-semibold text-sage-900 dark:text-sage-100 mb-4">Invite New Member</h3>
    <form hx-post="/household/invite" hx-target="#invite-message" hx-swap="outerHTML">
      <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
      <div class="mb-4">
        <label for="invite-email" class="block text-sm font-medium text-sage-700 dark:text-sage-300 mb-2">Email</label>
        <input type="email" id="invite-email" name="invite_email" required
               class="w-full px-3 py-2 border border-sage-300 dark:border-sage-600 rounded-md shadow-sm focus:outline-none focus:ring-sage-500 focus:border-sage-500 dark:bg-sage-700 dark:text-sage-100">
      </div>
      <button type="submit" class="bg-sage-600 hover:bg-sage-700 text-white font-medium px-4 py-2 rounded-md transition-colors">
        Send Invitation
      </button>
    </form>
    <div id="invite-message"></div>
  </div>
</div>
{% endblock %}
