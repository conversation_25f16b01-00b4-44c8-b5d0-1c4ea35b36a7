use actix_web::{test, web, App};
use garden_planner_web::routes;
use garden_planner_web::utils::templates::TEMPLATES;
use std::fs;
use std::path::Path;

#[actix_web::test]
async fn test_template_engine_initialization() {
    // Test that the template engine initializes successfully
    let templates = &*TEMPLATES;
    assert!(templates.is_ok(), "Template engine should initialize successfully");
}

#[actix_web::test]
async fn test_all_critical_templates_exist() {
    let templates = &*TEMPLATES;
    assert!(templates.is_ok(), "Template engine should be initialized");
    
    let tera = templates.as_ref().unwrap();
    
    // Critical templates that must exist
    let critical_templates = vec![
        "index.html",
        "base.html",
        "auth/login.html",
        "auth/register.html",
        "errors/404.html",
        "errors/500.html",
        "errors/403.html",
        "households/list.html",
        "households/view.html",
        "plants/list.html",
        "seeds/list.html",
        "admin/herba_database.html",
        "admin/scraper_result.html",
        "property/wizard.html",
        "wizard/create_property.html",
    ];
    
    for template_name in critical_templates {
        assert!(
            tera.get_template(template_name).is_ok(),
            "Critical template '{}' should exist and be parseable",
            template_name
        );
    }
}

#[actix_web::test]
async fn test_template_syntax_validation() {
    let templates = &*TEMPLATES;
    assert!(templates.is_ok(), "Template engine should be initialized");
    
    let tera = templates.as_ref().unwrap();
    
    // Test that all templates can be parsed without syntax errors
    let template_names: Vec<String> = tera.get_template_names().collect();
    
    for template_name in template_names {
        let template_result = tera.get_template(&template_name);
        assert!(
            template_result.is_ok(),
            "Template '{}' should have valid syntax: {:?}",
            template_name,
            template_result.err()
        );
    }
}

#[actix_web::test]
async fn test_base_template_functionality() {
    let templates = &*TEMPLATES;
    assert!(templates.is_ok(), "Template engine should be initialized");
    
    let tera = templates.as_ref().unwrap();
    
    // Test that base.html can be rendered with minimal context
    let mut context = tera::Context::new();
    context.insert("title", "Test Title");
    context.insert("is_authenticated", &false);
    context.insert("username", "");
    context.insert("user_role", "");
    
    let result = tera.render("base.html", &context);
    assert!(
        result.is_ok(),
        "base.html should render successfully: {:?}",
        result.err()
    );
    
    let rendered = result.unwrap();
    assert!(rendered.contains("Test Title"), "Rendered template should contain the title");
    assert!(rendered.contains("Garden Planner"), "Rendered template should contain the site name");
}

#[actix_web::test]
async fn test_index_template_rendering() {
    let templates = &*TEMPLATES;
    assert!(templates.is_ok(), "Template engine should be initialized");
    
    let tera = templates.as_ref().unwrap();
    
    // Test index.html rendering with guest context
    let mut context = tera::Context::new();
    context.insert("is_authenticated", &false);
    context.insert("username", "");
    context.insert("user_role", "");
    
    let result = tera.render("index.html", &context);
    assert!(
        result.is_ok(),
        "index.html should render successfully: {:?}",
        result.err()
    );
    
    let rendered = result.unwrap();
    assert!(rendered.contains("Welcome to Garden Planner"), "Index should contain welcome message");
    assert!(rendered.contains("Get Started"), "Index should contain get started button for guests");
}

#[actix_web::test]
async fn test_error_templates_rendering() {
    let templates = &*TEMPLATES;
    assert!(templates.is_ok(), "Template engine should be initialized");
    
    let tera = templates.as_ref().unwrap();
    
    let error_templates = vec!["errors/404.html", "errors/500.html", "errors/403.html"];
    
    for template_name in error_templates {
        let mut context = tera::Context::new();
        context.insert("is_authenticated", &false);
        context.insert("username", "");
        context.insert("user_role", "");
        context.insert("error_message", "Test error message");
        
        let result = tera.render(template_name, &context);
        assert!(
            result.is_ok(),
            "Error template '{}' should render successfully: {:?}",
            template_name,
            result.err()
        );
    }
}

#[actix_web::test]
async fn test_auth_templates_rendering() {
    let templates = &*TEMPLATES;
    assert!(templates.is_ok(), "Template engine should be initialized");
    
    let tera = templates.as_ref().unwrap();
    
    let auth_templates = vec!["auth/login.html", "auth/register.html"];
    
    for template_name in auth_templates {
        let mut context = tera::Context::new();
        context.insert("is_authenticated", &false);
        context.insert("username", "");
        context.insert("user_role", "");
        context.insert("csrf_token", "test_token");
        
        let result = tera.render(template_name, &context);
        assert!(
            result.is_ok(),
            "Auth template '{}' should render successfully: {:?}",
            template_name,
            result.err()
        );
        
        let rendered = result.unwrap();
        assert!(rendered.contains("test_token"), "Auth template should contain CSRF token");
    }
}

#[actix_web::test]
async fn test_list_templates_with_empty_data() {
    let templates = &*TEMPLATES;
    assert!(templates.is_ok(), "Template engine should be initialized");
    
    let tera = templates.as_ref().unwrap();
    
    // Test plants list with empty data
    let mut context = tera::Context::new();
    context.insert("is_authenticated", &true);
    context.insert("username", "testuser");
    context.insert("user_role", "user");
    context.insert("plants", &Vec::<serde_json::Value>::new());
    context.insert("plants_with_latin_names", &0);
    context.insert("plants_with_varieties", &0);
    
    let result = tera.render("plants/list.html", &context);
    assert!(
        result.is_ok(),
        "plants/list.html should render with empty data: {:?}",
        result.err()
    );
    
    // Test seeds list with empty data
    let mut context = tera::Context::new();
    context.insert("is_authenticated", &true);
    context.insert("username", "testuser");
    context.insert("user_role", "user");
    context.insert("seeds", &Vec::<serde_json::Value>::new());
    context.insert("fresh_seeds_count", &0);
    context.insert("expiring_soon_count", &0);
    context.insert("expired_seeds_count", &0);
    
    let result = tera.render("seeds/list.html", &context);
    assert!(
        result.is_ok(),
        "seeds/list.html should render with empty data: {:?}",
        result.err()
    );
}

#[actix_web::test]
async fn test_template_files_exist_on_filesystem() {
    // Verify that template files actually exist on the filesystem
    let template_dir = Path::new("templates");
    assert!(template_dir.exists(), "Templates directory should exist");
    
    let critical_files = vec![
        "templates/index.html",
        "templates/base.html",
        "templates/auth/login.html",
        "templates/auth/register.html",
        "templates/errors/404.html",
        "templates/errors/500.html",
        "templates/errors/403.html",
    ];
    
    for file_path in critical_files {
        let path = Path::new(file_path);
        assert!(path.exists(), "Critical template file '{}' should exist", file_path);
        
        // Verify file is readable
        let content = fs::read_to_string(path);
        assert!(content.is_ok(), "Template file '{}' should be readable", file_path);
        
        // Verify file is not empty
        let content = content.unwrap();
        assert!(!content.trim().is_empty(), "Template file '{}' should not be empty", file_path);
    }
}

#[actix_web::test]
async fn test_template_inheritance() {
    let templates = &*TEMPLATES;
    assert!(templates.is_ok(), "Template engine should be initialized");
    
    let tera = templates.as_ref().unwrap();
    
    // Test that templates extending base.html work correctly
    let mut context = tera::Context::new();
    context.insert("is_authenticated", &false);
    context.insert("username", "");
    context.insert("user_role", "");
    context.insert("csrf_token", "test_token");
    
    let result = tera.render("auth/login.html", &context);
    assert!(result.is_ok(), "Template inheritance should work");
    
    let rendered = result.unwrap();
    assert!(rendered.contains("<!DOCTYPE html>"), "Should contain DOCTYPE from base template");
    assert!(rendered.contains("Garden Planner"), "Should contain site name from base template");
}
