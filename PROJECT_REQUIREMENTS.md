# Garden Planner Web Application - Comprehensive Project Requirements

## Project Overview

The Garden Planner is a comprehensive web application for managing garden properties, plants, seasonal planning, and collaborative household management. Built with Rust (Actix-web), SQLite, and modern web technologies, it features a Material 3 design with sage-tinted pastels and supports multi-user collaboration.

## Core Architecture

### Technology Stack
- **Backend**: Rust with Actix-web framework
- **Database**: SQLite with Diesel ORM
- **Frontend**: HTML5, CSS3 (Tailwind CSS), JavaScript (ES6+)
- **3D Visualization**: Three.js for property visualization
- **Authentication**: Session-based with bcrypt password hashing
- **Templating**: Tera template engine

### Design System
- **Theme**: Pastel sage-tinted Material 3 design
- **Dark/Light Mode**: Toggle functionality with localStorage persistence
- **Responsive**: Mobile-first design with responsive breakpoints
- **Accessibility**: WCAG 2.1 AA compliance target

## User Management & Authentication

### User Roles Hierarchy
1. **Superadmin**: First registered user, full system access
2. **Admin**: User management, system configuration
3. **Moderator**: Content moderation, HerbaDB management
4. **Commenter**: Basic user with commenting privileges
5. **Viewer**: Read-only access

### Authentication Features
- User registration and login
- Password hashing with bcrypt
- Session management with secure cookies
- Rate limiting for login attempts
- CSRF protection on all forms

## Household Management System

### Household Architecture
- **Hierarchy**: 1 user → N households → N properties → N floors → N growing areas → N growing spots
- **Ownership**: Each household has an owner (creator)
- **Multi-household Support**: Users can belong to multiple households

### Household Sharing Permissions
1. **View**: Read-only access for showing off/bragging
2. **Comment + Notifications**: Care-taking access with notification rights
3. **Manage**: Full management rights equivalent to creator

### Household Features
- Household creation and management
- Member invitation system with role-based permissions
- Household switching with hover popup interface
- Property sharing within households
- Collaborative planning and management

## Property Management

### Property Structure
- **Basic Info**: Name, type, floors, areas (inside/outside)
- **Multi-floor Support**: Ground floor (0) and additional floors (1, 2, ...)
- **Outside Areas**: Floor -1 for outdoor spaces
- **Ownership**: Properties belong to households and have individual owners

### Property Creation Wizard
1. **Step 1**: Basic property information
2. **Step 2**: Property boundary drawing
3. **Step 3**: Growing areas definition
4. **Step 4**: Review and confirmation

### Drawing Tools
- **Freehand Drawing**: Cursor-accurate drawing with visible lines
- **Shape Tools**: Rectangle, circle, polygon tools
- **Grid System**: Toggle-able grid with snap functionality
- **Multi-floor Support**: Separate drawings per floor
- **Persistent Storage**: All shapes saved to database

### Property Visualization
- **2D Canvas**: Interactive property layout view
- **3D Visualization**: Three.js-based 3D property viewer
- **Floor Navigation**: Switch between floors and outside areas
- **Shape Rendering**: Property boundaries and growing areas
- **Statistics**: Area calculations and utilization rates

## Plant & Seed Management

### Plant Database (HerbaDB)
- **Global Database**: Shared botanical information
- **Personal Collections**: User-specific plant collections
- **Plant Information**: Scientific names, common names, growing requirements
- **Comprehensive Data**: Watering needs, light requirements, soil preferences

### HerbaDB Scraper System
- **Automatic Data Gathering**: Scrape plant information from public databases
- **Multiple Sources**: Missouri Botanical Garden, USDA Plants, RHS
- **Data Integration**: Automatic HerbaDB entry creation from scraped data
- **Re-scraping**: Update existing entries with new information
- **Manual Override**: Users can manually edit scraped information

### Seed Inventory
- **Seed Tracking**: Acquisition year, expiration year, viability
- **Origin Tracking**: Source and acquisition details
- **Expiration Alerts**: Visual indicators for expiring seeds
- **HerbaDB Integration**: Link seeds to plant database entries

### Wishlist System
- **Plant Wishlist**: Plants users want to grow
- **Seed Wishlist**: Seeds users want to acquire
- **Priority System**: Ranking system for wishlist items
- **Notes**: Personal notes for each wishlist item

## Season Planning & Management

### Season Management
- **Season Creation**: Define growing seasons with start/end dates
- **Automatic Seasons**: System-generated seasonal planning
- **Season Plans**: Detailed planting and harvesting schedules

### Automated Season Planner
- **Optimization Goals**: Yield maximization, space efficiency, nutrient rotation
- **Plant Placement**: Optimal positioning based on plant requirements
- **Companion Planting**: Beneficial plant combinations
- **Succession Planting**: Continuous harvest planning
- **Nutrient Management**: Soil health and rotation planning

### Growing Spots & Areas
- **Growing Areas**: Defined regions within properties
- **Growing Spots**: Individual planting locations within areas
- **Capacity Planning**: Maximum plant density calculations
- **Environmental Factors**: Light, water, soil considerations

## Notification System

### Plant Care Notifications
- **Watering Reminders**: Based on plant-specific needs
- **Fertilizing Schedules**: Nutrient application timing
- **Harvest Alerts**: Optimal harvest time notifications
- **Seasonal Tasks**: Planting, pruning, maintenance reminders

### Sharing Notifications
- **Household Invitations**: New member notifications
- **Property Sharing**: Access granted/revoked notifications
- **Collaboration Updates**: Changes to shared properties
- **Care-taking Alerts**: For users with comment/notification permissions

## Data Models & Relationships

### Core Entities
- **Users**: Authentication and profile information
- **Households**: Collaborative groups
- **Properties**: Physical garden spaces
- **Plants**: Botanical entities with growing information
- **Seeds**: Inventory items linked to plants
- **Seasons**: Time-based planning periods
- **Notifications**: Scheduled alerts and reminders

### Relationship Mapping
- Users ↔ Households (many-to-many with roles)
- Households → Properties (one-to-many)
- Properties → Floors → Growing Areas → Growing Spots
- Plants ↔ HerbaDB (linking personal to global data)
- Users → Wishlists → Plants/Seeds
- Seasons → Season Plans → Plant Placements

## API & Integration

### Internal APIs
- RESTful endpoints for all major operations
- JSON data exchange for AJAX operations
- Session-based authentication for API access
- CSRF protection on state-changing operations

### External Integrations
- **Plant Data Sources**: Missouri Botanical Garden, USDA Plants, RHS
- **Weather APIs**: Future integration for local weather data
- **Mapping Services**: Potential integration for property location

## Security & Privacy

### Security Measures
- **Password Security**: bcrypt hashing with salt
- **Session Security**: Secure cookie configuration
- **CSRF Protection**: Tokens on all forms
- **Rate Limiting**: Login attempt throttling
- **Input Validation**: Server-side validation for all inputs

### Privacy Considerations
- **Data Ownership**: Users own their data
- **Sharing Controls**: Granular permission system
- **Data Retention**: User-controlled data lifecycle
- **Export Capabilities**: Data portability features

## Performance & Scalability

### Performance Optimizations
- **Database Indexing**: Optimized queries with proper indexes
- **Caching Strategy**: Template and data caching
- **Asset Optimization**: Minified CSS/JS, optimized images
- **Lazy Loading**: Progressive data loading for large datasets

### Scalability Considerations
- **Database Design**: Normalized schema with efficient relationships
- **Connection Pooling**: Database connection management
- **Horizontal Scaling**: Architecture supports load balancing
- **Resource Management**: Efficient memory and CPU usage

## Testing Strategy

### Test Coverage
- **Unit Tests**: Core business logic testing
- **Integration Tests**: Database and API testing
- **End-to-End Tests**: Complete user workflow testing
- **Performance Tests**: Load and stress testing

### Test Scenarios
- User registration and authentication flows
- Household creation and sharing workflows
- Property wizard and visualization
- Plant database and scraper functionality
- Season planning and notification systems

## Deployment & Operations

### Deployment Requirements
- **Environment**: Linux-based server environment
- **Dependencies**: Rust toolchain, SQLite, web server
- **Configuration**: Environment variables for sensitive data
- **Monitoring**: Application and system monitoring

### Operational Procedures
- **Database Migrations**: Version-controlled schema changes
- **Backup Strategy**: Regular database backups
- **Update Process**: Rolling updates with minimal downtime
- **Error Handling**: Comprehensive error logging and alerting

## Future Enhancements

### Planned Features
- **Mobile Application**: Native mobile app development
- **Weather Integration**: Local weather data and alerts
- **IoT Integration**: Sensor data for automated monitoring
- **Advanced Analytics**: Growth tracking and yield analysis
- **Community Features**: Public sharing and community gardens

### Technical Improvements
- **Microservices**: Service decomposition for scalability
- **Real-time Updates**: WebSocket integration for live updates
- **Advanced Caching**: Redis integration for improved performance
- **API Gateway**: Centralized API management and security

## Compliance & Standards

### Web Standards
- **HTML5**: Semantic markup and accessibility
- **CSS3**: Modern styling with progressive enhancement
- **JavaScript ES6+**: Modern JavaScript features
- **Responsive Design**: Mobile-first approach

### Accessibility
- **WCAG 2.1 AA**: Target compliance level
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and structure
- **Color Contrast**: Sufficient contrast ratios

### Code Quality
- **Rust Best Practices**: Idiomatic Rust code
- **Documentation**: Comprehensive code documentation
- **Code Review**: Peer review process
- **Continuous Integration**: Automated testing and deployment

## Success Metrics

### User Engagement
- **User Registration**: New user acquisition rate
- **Active Users**: Daily/monthly active user metrics
- **Feature Adoption**: Usage statistics for key features
- **User Retention**: Long-term user engagement

### System Performance
- **Response Times**: API and page load performance
- **Uptime**: System availability metrics
- **Error Rates**: Application error tracking
- **Resource Usage**: Server resource utilization

### Business Objectives
- **User Satisfaction**: User feedback and ratings
- **Feature Completeness**: Implementation of planned features
- **Community Growth**: Household and sharing adoption
- **Data Quality**: Accuracy of plant and growing data

---

This document serves as the comprehensive specification for the Garden Planner web application, covering all aspects from technical architecture to user experience and future roadmap.
