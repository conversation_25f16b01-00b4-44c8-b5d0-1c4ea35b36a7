use actix_web::{HttpResponse, Result};
use actix_session::Session;
use tera::{Context, Tera};
use crate::utils::csrf::get_csrf_token;
use serde::Serialize;
use std::path::Path;

// Critical templates that must exist for the application to function
const CRITICAL_TEMPLATES: &[&str] = &[
    "index.html",
    "base.html",
    "auth/login.html",
    "auth/register.html",
    "errors/404.html",
    "errors/500.html",
    "errors/403.html",
    "households/list.html",
    "households/view.html",
    "plants/list.html",
    "seeds/list.html",
    "admin/herba_database.html",
    "admin/scraper_result.html",
    "property/wizard.html",
    "wizard/create_property.html",
];

lazy_static::lazy_static! {
    pub static ref TEMPLATES: Tera = {
        // Try multiple possible template paths
        let template_paths = vec![
            "templates/**/*.html",
            "./templates/**/*.html",
            "/home/<USER>/dev/garden_planner_web/templates/**/*.html"
        ];

        for path in template_paths {
            match Tera::new(path) {
                Ok(mut tera) => {
                    tera.autoescape_on(vec!["html"]);
                    println!("Successfully loaded templates from: {}", path);
                    return tera;
                }
                Err(e) => {
                    println!("Failed to load templates from {}: {}", path, e);
                    continue;
                }
            }
        }

        // If all paths fail, create empty Tera and log error
        println!("Template parsing error: Failed to load templates from any path");
        let mut tera = Tera::default();
        tera.autoescape_on(vec!["html"]);
        tera
    };
}

#[derive(Serialize)]
pub struct UserContext {
    pub user_id: Option<i32>,
    pub username: Option<String>,
    pub role: Option<String>,
    pub is_authenticated: bool,
    pub is_admin: bool,
    pub is_superadmin: bool,
    pub current_household_id: Option<i32>,
    pub current_household_name: Option<String>,
}

impl UserContext {
    pub fn from_session(session: &Session) -> Self {
        // Debug: Check all session entries
        println!("Session debug - checking session entries...");

        let user_id_result = session.get::<i32>("user_id");
        let username_result = session.get::<String>("username");
        let role_result = session.get::<String>("role");
        let current_household_id_result = session.get::<i32>("current_household_id");
        let current_household_name_result = session.get::<String>("current_household_name");

        println!("Session get results - user_id: {:?}, username: {:?}, role: {:?}",
                 user_id_result, username_result, role_result);

        let user_id = user_id_result.unwrap_or(None);
        let username = username_result.unwrap_or(None);
        let role = role_result.unwrap_or(None);
        let current_household_id = current_household_id_result.unwrap_or(None);
        let current_household_name = current_household_name_result.unwrap_or(None);
        let is_authenticated = user_id.is_some();

        let is_admin = role.as_ref().map_or(false, |r| r == "admin" || r == "superadmin");
        let is_superadmin = role.as_ref().map_or(false, |r| r == "superadmin");

        // Debug logging
        println!("UserContext::from_session - user_id: {:?}, username: {:?}, role: {:?}, is_authenticated: {}",
                 user_id, username, role, is_authenticated);

        UserContext {
            user_id,
            username,
            role,
            is_authenticated,
            is_admin,
            is_superadmin,
            current_household_id,
            current_household_name,
        }
    }
}

/// Add user context to a template context
pub fn add_user_context(context: &mut Context, session: &Session) {
    let user_context = UserContext::from_session(session);

    // Add individual fields for backward compatibility
    if let Some(ref username) = user_context.username {
        context.insert("username", username);
    } else {
        context.insert("username", "");
    }

    if let Some(ref role) = user_context.role {
        context.insert("role", role);
    } else {
        context.insert("role", "");
    }

    // Add the full user object for template conditionals
    context.insert("user", &user_context);
    context.insert("user_context", &user_context);
    context.insert("is_authenticated", &user_context.is_authenticated);
}

pub fn render_template(template_name: &str, context: &Context) -> Result<HttpResponse> {
    let body = TEMPLATES.render(template_name, context).map_err(|e| {
        println!("Template error: Failed to render '{}': {}", template_name, e);
        println!("Error details: {:?}", e);
        actix_web::error::ErrorInternalServerError(e)
    })?;
    Ok(HttpResponse::Ok().content_type("text/html").body(body))
}

/// Render a template with user context and CSRF token
///
/// # Arguments
/// * `template_name` - The name of the template to render
/// * `context` - The context to use for rendering
/// * `session` - The user's session
///
/// # Returns
/// * `Result<HttpResponse>` - The rendered template
pub fn render_template_with_context(template_name: &str, context: &mut Context, session: &Session) -> Result<HttpResponse> {
    // Add user context
    add_user_context(context, session);

    // Add CSRF token to context
    let csrf_token = get_csrf_token(session);
    context.insert("csrf_token", &csrf_token);

    render_template(template_name, context)
}

/// Render a template with CSRF token included in the context (legacy function)
///
/// # Arguments
/// * `template_name` - The name of the template to render
/// * `context` - The context to use for rendering
/// * `session` - The user's session
///
/// # Returns
/// * `Result<HttpResponse>` - The rendered template
pub fn render_template_with_csrf(template_name: &str, context: &mut Context, session: &Session) -> Result<HttpResponse> {
    render_template_with_context(template_name, context, session)
}

/// Validate that all critical templates exist and can be parsed
pub fn validate_critical_templates() -> std::result::Result<(), String> {
    println!("Validating critical templates...");

    // Check if templates directory exists
    if !Path::new("templates").exists() {
        return Err("Templates directory does not exist".to_string());
    }

    let mut missing_templates = Vec::new();
    let mut parsing_errors = Vec::new();

    for template_name in CRITICAL_TEMPLATES {
        // Check if template file exists on filesystem
        let template_path = format!("templates/{}", template_name);
        if !Path::new(&template_path).exists() {
            missing_templates.push(template_name.to_string());
            continue;
        }

        // Check if template can be parsed by Tera
        match TEMPLATES.get_template(template_name) {
            Ok(_) => {
                println!("✓ Template '{}' validated successfully", template_name);
            }
            Err(e) => {
                parsing_errors.push(format!("{}: {}", template_name, e));
            }
        }
    }

    if !missing_templates.is_empty() || !parsing_errors.is_empty() {
        let mut error_msg = String::new();

        if !missing_templates.is_empty() {
            error_msg.push_str(&format!("Missing templates: {}\n", missing_templates.join(", ")));
        }

        if !parsing_errors.is_empty() {
            error_msg.push_str(&format!("Template parsing errors:\n{}", parsing_errors.join("\n")));
        }

        return Err(error_msg);
    }

    println!("All critical templates validated successfully!");
    Ok(())
}

/// Create a fallback error response when templates fail
pub fn create_fallback_error_response(status_code: u16, message: &str) -> HttpResponse {
    let html = format!(
        r#"<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error - Garden Planner</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
        .error-container {{ max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #d32f2f; margin-bottom: 20px; }}
        p {{ color: #666; line-height: 1.6; }}
        .error-code {{ font-size: 48px; font-weight: bold; color: #d32f2f; margin-bottom: 10px; }}
        .back-link {{ display: inline-block; margin-top: 20px; color: #1976d2; text-decoration: none; }}
        .back-link:hover {{ text-decoration: underline; }}
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">{}</div>
        <h1>Oops! Something went wrong</h1>
        <p>{}</p>
        <p>We're experiencing technical difficulties. Please try again later or contact support if the problem persists.</p>
        <a href="/" class="back-link">← Return to Home</a>
    </div>
</body>
</html>"#,
        status_code, message
    );

    match status_code {
        404 => HttpResponse::NotFound().content_type("text/html").body(html),
        500 => HttpResponse::InternalServerError().content_type("text/html").body(html),
        403 => HttpResponse::Forbidden().content_type("text/html").body(html),
        _ => HttpResponse::InternalServerError().content_type("text/html").body(html),
    }
}
