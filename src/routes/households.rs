use actix_session::Session;
use actix_web::{web, HttpResponse, Result};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};

use crate::models::household::{Household, NewHousehold};
use crate::models::user_household::{UserHousehold, NewUserHousehold};
use crate::models::user::User;
use crate::models::property::Property;
use crate::schema::{households, user_households, users, properties};
use crate::utils::templates::{render_template_with_context};
use crate::utils::auth::is_authenticated;
use crate::DbPool;

#[derive(Deserialize)]
pub struct HouseholdForm {
    pub name: String,
}

#[derive(Deserialize)]
pub struct ShareHouseholdForm {
    pub username: String,
    pub role: String, // "manage", "comment", "view"
}

#[derive(Serialize)]
pub struct HouseholdViewModel {
    pub id: i32,
    pub name: String,
    pub owner_id: i32,
    pub is_owner: bool,
    pub user_role: String,
    pub member_count: i64,
}

// List user's households
pub async fn list_households(session: Session, pool: web::Data<DbPool>) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get households where user is a member
    let user_households = user_households::table
        .filter(user_households::user_id.eq(user_id))
        .inner_join(households::table)
        .select((households::all_columns, user_households::role))
        .load::<(Household, String)>(&mut conn)
        .expect("Error loading user households");

    let mut household_view_models = Vec::new();

    for (household, role) in user_households {
        // Count members
        let member_count = user_households::table
            .filter(user_households::household_id.eq(household.id))
            .count()
            .get_result::<i64>(&mut conn)
            .unwrap_or(0);

        household_view_models.push(HouseholdViewModel {
            id: household.id,
            name: household.name,
            owner_id: household.owner_id,
            is_owner: household.owner_id == user_id,
            user_role: role,
            member_count,
        });
    }

    let mut ctx = tera::Context::new();
    ctx.insert("households", &household_view_models);

    render_template_with_context("households/list.html", &mut ctx, &session)
}

// Show form to create new household
pub async fn new_household_form(session: Session) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let mut ctx = tera::Context::new();
    render_template_with_context("households/new.html", &mut ctx, &session)
}

// Create new household
pub async fn create_household(
    session: Session,
    form: web::Form<HouseholdForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Create household
    let new_household = NewHousehold {
        name: &form.name,
        owner_id: user_id,
    };

    diesel::insert_into(households::table)
        .values(&new_household)
        .execute(&mut conn)
        .expect("Error creating household");

    // Get the inserted household ID
    let household_id = households::table
        .filter(households::name.eq(&form.name))
        .filter(households::owner_id.eq(user_id))
        .select(households::id)
        .first::<i32>(&mut conn)
        .expect("Error getting household ID");

    // Add user as admin member
    let new_user_household = NewUserHousehold {
        user_id,
        household_id,
        role: "admin",
    };

    diesel::insert_into(user_households::table)
        .values(&new_user_household)
        .execute(&mut conn)
        .expect("Error adding user to household");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/households"))
        .finish())
}

// View household details
pub async fn view_household(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let household_id = path.into_inner();
    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Check if user has access to this household
    let user_household = user_households::table
        .filter(user_households::user_id.eq(user_id))
        .filter(user_households::household_id.eq(household_id))
        .first::<UserHousehold>(&mut conn)
        .optional()
        .expect("Error checking household access");

    if user_household.is_none() {
        return Ok(HttpResponse::Forbidden().body("Access denied"));
    }

    let household = households::table
        .find(household_id)
        .first::<Household>(&mut conn)
        .expect("Error loading household");

    // Get household members
    let members_data = user_households::table
        .filter(user_households::household_id.eq(household_id))
        .inner_join(users::table)
        .select((users::all_columns, user_households::role))
        .load::<(User, String)>(&mut conn)
        .expect("Error loading household members");

    // Transform the data for easier template access
    let members: Vec<serde_json::Value> = members_data.iter().map(|(user, role)| {
        serde_json::json!({
            "id": user.id,
            "username": user.username,
            "role": role
        })
    }).collect();

    let mut ctx = tera::Context::new();
    ctx.insert("household", &household);
    ctx.insert("members", &members);
    ctx.insert("user_role", &user_household.unwrap().role);
    ctx.insert("is_owner", &(household.owner_id == user_id));

    render_template_with_context("households/view.html", &mut ctx, &session)
}

pub async fn switch_household(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let household_id = path.into_inner();
    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Verify user has access to this household
    let user_household = user_households::table
        .filter(user_households::user_id.eq(user_id))
        .filter(user_households::household_id.eq(household_id))
        .first::<UserHousehold>(&mut conn)
        .optional()
        .expect("Error checking household membership");

    if user_household.is_none() {
        return Ok(HttpResponse::Forbidden().body("Access denied"));
    }

    // Get household name and store in session
    let household = households::table
        .find(household_id)
        .first::<Household>(&mut conn)
        .expect("Error loading household");

    // Store current household in session
    session.insert("current_household_id", household_id)?;
    session.insert("current_household_name", &household.name)?;

    Ok(HttpResponse::Found()
        .append_header(("Location", "/"))
        .finish())
}

pub async fn edit_household_form(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let household_id = path.into_inner();
    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Get household and verify ownership or admin access
    let household = households::table
        .find(household_id)
        .first::<Household>(&mut conn)
        .expect("Error loading household");

    let user_household = user_households::table
        .filter(user_households::user_id.eq(user_id))
        .filter(user_households::household_id.eq(household_id))
        .first::<UserHousehold>(&mut conn)
        .optional()
        .expect("Error checking household membership");

    if let Some(uh) = user_household {
        if household.owner_id != user_id && uh.role != "admin" {
            return Ok(HttpResponse::Forbidden().body("Access denied"));
        }
    } else {
        return Ok(HttpResponse::Forbidden().body("Access denied"));
    }

    let mut ctx = tera::Context::new();
    ctx.insert("household", &household);

    render_template_with_context("households/edit.html", &mut ctx, &session)
}

pub async fn update_household(
    session: Session,
    path: web::Path<i32>,
    form: web::Form<HouseholdForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let household_id = path.into_inner();
    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Verify ownership or admin access
    let household = households::table
        .find(household_id)
        .first::<Household>(&mut conn)
        .expect("Error loading household");

    let user_household = user_households::table
        .filter(user_households::user_id.eq(user_id))
        .filter(user_households::household_id.eq(household_id))
        .first::<UserHousehold>(&mut conn)
        .optional()
        .expect("Error checking household membership");

    if let Some(uh) = user_household {
        if household.owner_id != user_id && uh.role != "admin" {
            return Ok(HttpResponse::Forbidden().body("Access denied"));
        }
    } else {
        return Ok(HttpResponse::Forbidden().body("Access denied"));
    }

    // Update household
    diesel::update(households::table.filter(households::id.eq(household_id)))
        .set(households::name.eq(&form.name))
        .execute(&mut conn)
        .expect("Error updating household");

    Ok(HttpResponse::Found()
        .append_header(("Location", format!("/households/{}/view", household_id)))
        .finish())
}

pub async fn delete_household(
    session: Session,
    path: web::Path<i32>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let household_id = path.into_inner();
    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Verify ownership
    let household = households::table
        .find(household_id)
        .first::<Household>(&mut conn)
        .expect("Error loading household");

    if household.owner_id != user_id {
        return Ok(HttpResponse::Forbidden().body("Only the owner can delete a household"));
    }

    // Delete household (this will cascade to user_households)
    diesel::delete(households::table.filter(households::id.eq(household_id)))
        .execute(&mut conn)
        .expect("Error deleting household");

    Ok(HttpResponse::Found()
        .append_header(("Location", "/households"))
        .finish())
}

pub async fn share_household(
    session: Session,
    path: web::Path<i32>,
    form: web::Form<ShareHouseholdForm>,
    pool: web::Data<DbPool>,
) -> Result<HttpResponse> {
    if !is_authenticated(&session) {
        return Ok(HttpResponse::Found()
            .append_header(("Location", "/auth/login"))
            .finish());
    }

    let household_id = path.into_inner();
    let user_id = session.get::<i32>("user_id")?.unwrap();
    let mut conn = pool.get().expect("Couldn't get DB connection from pool");

    // Verify ownership or admin access
    let household = households::table
        .find(household_id)
        .first::<Household>(&mut conn)
        .expect("Error loading household");

    let user_household = user_households::table
        .filter(user_households::user_id.eq(user_id))
        .filter(user_households::household_id.eq(household_id))
        .first::<UserHousehold>(&mut conn)
        .optional()
        .expect("Error checking household membership");

    if let Some(uh) = user_household {
        if household.owner_id != user_id && uh.role != "admin" {
            return Ok(HttpResponse::Forbidden().body("Access denied"));
        }
    } else {
        return Ok(HttpResponse::Forbidden().body("Access denied"));
    }

    // Find the user to share with
    let target_user = users::table
        .filter(users::username.eq(&form.username))
        .first::<User>(&mut conn)
        .optional()
        .expect("Error finding user");

    if let Some(user) = target_user {
        // Check if user is already a member
        let existing_membership = user_households::table
            .filter(user_households::user_id.eq(user.id))
            .filter(user_households::household_id.eq(household_id))
            .first::<UserHousehold>(&mut conn)
            .optional()
            .expect("Error checking existing membership");

        if existing_membership.is_none() {
            // Add user to household with new permission levels
            let role_str = match form.role.as_str() {
                "manage" => "manage",    // Full management rights equivalent to creator
                "comment" => "comment",  // Care-taking access with notification rights
                "view" => "view",        // Read-only access for showing off/bragging
                "admin" => "admin",      // Legacy admin role
                "member" => "member",    // Legacy member role
                "viewer" => "viewer",    // Legacy viewer role
                _ => "view",             // Default to view-only
            };
            let new_membership = NewUserHousehold {
                user_id: user.id,
                household_id,
                role: role_str,
            };

            diesel::insert_into(user_households::table)
                .values(&new_membership)
                .execute(&mut conn)
                .expect("Error adding user to household");
        }
    }

    Ok(HttpResponse::Found()
        .append_header(("Location", format!("/households/{}/view", household_id)))
        .finish())
}

pub fn init(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/households")
            .route("", web::get().to(list_households))
            .route("/new", web::get().to(new_household_form))
            .route("/create", web::post().to(create_household))
            .route("/{id}/view", web::get().to(view_household))
            .route("/{id}/switch", web::post().to(switch_household))
            .route("/{id}/edit", web::get().to(edit_household_form))
            .route("/{id}/update", web::post().to(update_household))
            .route("/{id}/delete", web::post().to(delete_household))
            .route("/{id}/share", web::post().to(share_household)),
    );
}
